import type {
  HybridView,
  HybridViewProps,
  HybridViewMethods,
} from 'react-native-nitro-modules';

export interface AtcButtonProps extends HybridViewProps {
  qty: bigint;
  text?: string;
  productJson?: string;

  onListener: (event: ButtonEvent) => void;
  // removeListeners(): void;

}

export interface ButtonEvent {
  id: string;
  productId: number;
  type: string; //'quantity_change' | 'status_change' | 'animation_trigger';
  action?: string; //'increase' | 'decrease' | 'numClick' | 'expand' | 'collapse';
  quantity?: number;
  status?: string;
  timestamp: number;
  animationType?: string; //'add_to_cart' | 'remove_from_cart' | 'quantity_update' | 'expand_editor';
}

export interface AtcButtonMethods extends HybridViewMethods {
  onQuantityChange(quantity: number, productId: number): void;
  onStatusChange(status: string, productId: number): void;


  // 新增动画相关方法
  triggerAnimation(animationType: string, productId: number): void;
}

export type WeeeAtcButton2 = HybridView<AtcButtonProps, AtcButtonMethods>;
