import type {TestResult} from './types/TestResult';
import React, {forwardRef, useRef, useImperativeHandle} from 'react';
import {ViewStyle, View} from 'react-native';

import WeeeAtcButton2Config from '../nitrogen/generated/shared/json/WeeeAtcButton2Config.json';

import {getHostComponent} from 'react-native-nitro-modules';
import type {
  AtcButtonProps,
  AtcButtonMethods,
  ButtonEvent,
} from './nitro/WeeeAtcButton2.nitro';

// ==================== 类型导出 ====================

import Toast from './utils/toast';
import Cache from './utils/cache';
import Network from './utils/network';
import Route from './utils/route';
import Sync from './utils/sync';
import {WeeeResponse} from './types/WeeeResponse';

export type {WeeeResponse, TestResult};

export {Network, Toast, Cache, Route, Sync};

// ==================== WeeeAtcButton2 组件 ====================

// Create the hybrid view component using getHostComponent
const WeeeAtcButton2Native = getHostComponent<AtcButtonProps, AtcButtonMethods>(
  'WeeeAtcButton2',
  () => WeeeAtcButton2Config,
);

// Define props interface for the wrapped component
export interface WeeeAtcButton2Props extends Omit<AtcButtonProps, 'style'> {
  className?: string;
  style?: ViewStyle;
  onQuantityChange?: (event: {quantity: number; productId: number}) => void;
  onStatusChange?: (event: {status: string; productId: number}) => void;
  onAnimationTrigger?: (event: {animationType: string; productId: number}) => void;
  onCartUpdate?: (productId: number, quantity: number, action: 'add' | 'remove' | 'update') => void;
}

// Wrapped component that supports NativeWind className
export const WeeeAtcButton2 = forwardRef<any, WeeeAtcButton2Props>(
  ({className, style, onQuantityChange, onStatusChange, onAnimationTrigger, onCartUpdate, ...restProps}, ref) => {
    // 从 restProps 中排除 onListener，因为我们要自己处理
    const { onListener, ...props } = restProps;
    const nativeRef = useRef<any>(null);

    useImperativeHandle(ref, () => nativeRef.current);

    // 创建事件监听器
    const eventListener = (event: ButtonEvent) => {
      try {
        const { type, productId, quantity, status, action, animationType } = event;

        switch (type) {
          case 'quantity_change':
            onQuantityChange?.({ quantity: quantity || 0, productId });

            // 触发购物车更新回调
            if (onCartUpdate && action) {
              let cartAction: 'add' | 'remove' | 'update' = 'update';
              if (action === 'increase' && quantity === 1) {
                cartAction = 'add';
              } else if (action === 'decrease' && quantity === 0) {
                cartAction = 'remove';
              }
              onCartUpdate(productId, quantity || 0, cartAction);
            }
            break;

          case 'status_change':
            onStatusChange?.({ status: status || action || '', productId });
            break;

          case 'animation_trigger':
            onAnimationTrigger?.({ animationType: animationType || '', productId });
            break;
        }
      } catch (error) {
        console.error('WeeeAtcButton2 event handling error:', error);
      }
    };

    if (className) {
      return (
        <View className={className} style={style}>
          <WeeeAtcButton2Native
            ref={nativeRef}
            style={{flex: 1, ...style}}
            onListener={{ f: eventListener }}
            {...props}
            hybridRef={{f: (ref) => {
              ref.onQuantityChange(100, 200)
            }}}
          />
        </View>
      );
    }
    return (
      <WeeeAtcButton2Native
        ref={nativeRef}
        className={className}
        style={style}
        onListener={{ f: eventListener }}
        {...props}
      />
    );
  },
);

WeeeAtcButton2.displayName = 'WeeeAtcButton2';
