# WeeeAtcButton2 展开状态修复测试

## 问题描述
WeeeAtcButton2 在有数量的商品上显示为收缩状态（蓝色圆圈显示数量），而不是展开状态（显示 - 数量 + 按钮）。

## 根本原因
1. `OpHelper.helperOp` 调用 `setOpNum(num)` 方法
2. `setOpNum` 方法强制调用 `collapse()`，导致有数量的商品显示为收缩状态
3. 需要在 `OpHelper.helperOp` 之后重新设置正确的展开/收缩状态

## 修复方案
在 `CartOpLayoutUIComponent.initializeWithProductData` 方法中：
1. 调用 `OpHelper.helperOp` 设置基础状态
2. 检查当前数量，如果 > 0，调用 `setTextNum(quantity, false)` 确保展开状态

## 修复代码
```java
// 在 OpHelper.helperOp 之后添加
int currentQuantity = cartOpLayout.getTextNum();
if (currentQuantity > 0) {
    // Use setTextNum to ensure correct expand state for items with quantity
    cartOpLayout.setTextNum(currentQuantity, false);
}
```

## 测试步骤
1. 打开购物车页面
2. 查看已加购商品的显示状态
3. 确认有数量的商品显示为展开状态（- 数量 + 按钮）
4. 确认无数量的商品显示为收缩状态（+ 按钮）

## 预期结果
- 有数量的商品：显示展开状态（- 数量 + 按钮）
- 无数量的商品：显示收缩状态（+ 按钮）
