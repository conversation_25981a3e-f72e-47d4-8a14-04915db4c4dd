# WeeeAtcButton2 动画实现指南

## 概述

WeeeAtcButton2 组件现在支持完整的事件回调机制，能够在原生端加购时通知 React Native 页面刷新，并实现从0加到1和从1减到0的动画效果。

## 实现流程

### 1. Android 端事件发送

当用户在原生端进行加购/减购操作时，Android 端会：

1. **执行业务逻辑**：更新购物车数量
2. **发送事件**：通过 `onListener` 回调发送事件到 React Native
3. **触发动画**：根据操作类型触发相应的动画事件

```kotlin
// Android 端关键代码
private fun executeIncreaseOperation() {
    // ... 业务逻辑 ...
    
    // 发送数量变化事件
    onQuantityChange(newQuantity.toDouble(), product.id.toDouble())
    onStatusChange("increase", product.id.toDouble())
    
    // 触发动画事件
    if (currentQuantity == 0 && newQuantity > 0) {
        triggerAnimation("add_to_cart", product.id.toDouble())
    } else {
        triggerAnimation("quantity_update", product.id.toDouble())
    }
}
```

### 2. React Native 端事件接收

React Native 端通过 `onListener` 接收事件并处理：

```typescript
const eventListener = (event: ButtonEvent) => {
  const { type, productId, quantity, action, animationType } = event;
  
  switch (type) {
    case 'quantity_change':
      onQuantityChange?.({ quantity: quantity || 0, productId });
      // 触发购物车更新
      if (onCartUpdate && action) {
        let cartAction: 'add' | 'remove' | 'update' = 'update';
        if (action === 'increase' && quantity === 1) {
          cartAction = 'add';
        } else if (action === 'decrease' && quantity === 0) {
          cartAction = 'remove';
        }
        onCartUpdate(productId, quantity || 0, cartAction);
      }
      break;
      
    case 'animation_trigger':
      onAnimationTrigger?.({ animationType: animationType || '', productId });
      break;
  }
};
```

## 动画类型说明

### 1. add_to_cart (首次加购)
- **触发时机**：数量从 0 变为 1
- **建议动画**：放大缩小效果，突出加购操作
- **实现示例**：
```typescript
case 'add_to_cart':
  Animated.sequence([
    Animated.timing(scaleAnim, {
      toValue: 1.2,
      duration: 150,
      useNativeDriver: true,
    }),
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 150,
      useNativeDriver: true,
    }),
  ]).start();
  break;
```

### 2. remove_from_cart (移除商品)
- **触发时机**：数量变为 0
- **建议动画**：淡出效果，表示商品被移除
- **实现示例**：
```typescript
case 'remove_from_cart':
  Animated.timing(opacityAnim, {
    toValue: 0.3,
    duration: 200,
    useNativeDriver: true,
  }).start(() => {
    Animated.timing(opacityAnim, {
      toValue: 1,
      duration: 200,
      useNativeDriver: true,
    }).start();
  });
  break;
```

### 3. quantity_update (数量更新)
- **触发时机**：数量增减但不为 0
- **建议动画**：轻微震动或缩放效果
- **实现示例**：
```typescript
case 'quantity_update':
  Animated.sequence([
    Animated.timing(scaleAnim, {
      toValue: 1.05,
      duration: 100,
      useNativeDriver: true,
    }),
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 100,
      useNativeDriver: true,
    }),
  ]).start();
  break;
```

### 4. expand_editor (展开编辑器)
- **触发时机**：点击数量按钮展开编辑器
- **建议动画**：弹性展开效果
- **实现示例**：
```typescript
case 'expand_editor':
  Animated.spring(scaleAnim, {
    toValue: 1.1,
    friction: 3,
    tension: 40,
    useNativeDriver: true,
  }).start(() => {
    Animated.spring(scaleAnim, {
      toValue: 1,
      friction: 3,
      tension: 40,
      useNativeDriver: true,
    }).start();
  });
  break;
```

### 5. collapse_editor (收起编辑器)
- **触发时机**：数量减到0时自动收起编辑器
- **建议动画**：缩小收起效果
- **实现示例**：
```typescript
case 'collapse_editor':
  Animated.timing(scaleAnim, {
    toValue: 0.9,
    duration: 150,
    easing: Easing.in(Easing.ease),
    useNativeDriver: true,
  }).start(() => {
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 150,
      easing: Easing.out(Easing.ease),
      useNativeDriver: true,
    }).start();
  });
  break;
```

## 使用示例

### 基本用法

```tsx
import { WeeeAtcButton2 } from 'weee-native';

function ProductItem({ product }) {
  const [quantity, setQuantity] = useState(product.quantity);
  const scaleAnim = useRef(new Animated.Value(1)).current;

  const handleQuantityChange = useCallback((event) => {
    setQuantity(event.quantity);
  }, []);

  const handleAnimationTrigger = useCallback((event) => {
    // 根据动画类型执行相应动画
    switch (event.animationType) {
      case 'add_to_cart':
        // 执行加购动画
        break;
      case 'remove_from_cart':
        // 执行移除动画
        break;
      // ... 其他动画类型
    }
  }, []);

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <WeeeAtcButton2
        qty={BigInt(quantity)}
        productJson={JSON.stringify(product)}
        onQuantityChange={handleQuantityChange}
        onAnimationTrigger={handleAnimationTrigger}
        onCartUpdate={(productId, quantity, action) => {
          // 更新购物车
          console.log(`Cart ${action}: ${quantity} for product ${productId}`);
        }}
      />
    </Animated.View>
  );
}
```

## 调试和测试

### 1. 日志输出
组件会输出详细的日志信息：

```
🎯 WeeeAtcButton2 received event: {type: "quantity_change", ...}
📊 Quantity changed: 1 for product 123, action: increase
🛒 First time adding to cart
🎬 Animation triggered: add_to_cart for product 123
```

### 2. 测试组件
使用 `WeeeAtcButton2Example.tsx` 组件进行测试：

```tsx
import WeeeAtcButton2Example from './src/component/WeeeAtcButton2Example';

// 在你的应用中使用
<WeeeAtcButton2Example />
```

## 最佳实践

1. **使用 useNativeDriver**：提升动画性能
2. **适度的动画时长**：150-300ms 为佳
3. **错误处理**：在事件回调中添加 try-catch
4. **内存管理**：组件卸载时清理动画
5. **用户体验**：避免过于频繁或夸张的动画

## 注意事项

- 事件回调是异步的，不要依赖执行顺序
- 动画触发可能频繁，注意防抖处理
- 确保在组件卸载时清理事件监听器
- 测试时注意检查事件是否正确触发和处理
