buildscript {
  ext.getExtOrDefault = {name ->
    return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['WeeeNetwork_' + name]
  }

  repositories {
    google()
    mavenCentral()
  }

  dependencies {
    classpath "com.android.tools.build:gradle:8.7.2"
    // noinspection DifferentKotlinGradleVersion
    classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${getExtOrDefault('kotlinVersion')}"
  }
}


apply plugin: "com.android.library"
apply plugin: "kotlin-android"

apply plugin: "com.facebook.react"

apply from: '../nitrogen/generated/android/WeeeNative+autolinking.gradle'


def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties["WeeeNetwork_" + name]).toInteger()
}

def reactNativeArchitectures() {
  def value = rootProject.getProperties().get("reactNativeArchitectures")
  return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

android {
  namespace "com.sayweee.react"

  compileSdkVersion getExtOrIntegerDefault("compileSdkVersion")

  defaultConfig {
    minSdkVersion getExtOrIntegerDefault("minSdkVersion")
    targetSdkVersion getExtOrIntegerDefault("targetSdkVersion")

    externalNativeBuild {
      cmake {
        cppFlags "-frtti -fexceptions -Wall -fstack-protector-all"
        arguments "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"
        abiFilters (*reactNativeArchitectures())

        buildTypes {
          debug {
            cppFlags "-O1 -g"
          }
          release {
            cppFlags "-O2"
          }
        }
      }
    }
  }

  externalNativeBuild {
    cmake {
      path "CMakeLists.txt"
    }
  }

  packagingOptions {
    excludes = [
            "META-INF",
            "META-INF/**",
            "**/libc++_shared.so",
            "**/libfbjni.so",
            "**/libjsi.so",
            "**/libfolly_json.so",
            "**/libfolly_runtime.so",
            "**/libglog.so",
            "**/libhermes.so",
            "**/libhermes-executor-debug.so",
            "**/libhermes_executor.so",
            "**/libreactnative.so",
            "**/libreactnativejni.so",
            "**/libturbomodulejsijni.so",
            "**/libreact_nativemodule_core.so",
            "**/libjscexecutor.so"
    ]
  }

  buildFeatures {
    buildConfig true
    prefab true
  }

  buildTypes {
    release {
      minifyEnabled false
    }
  }

  lintOptions {
    disable "GradleCompatible"
  }

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }

  sourceSets {
    main {
      java.srcDirs += [
        "generated/java",
        "generated/jni"
      ]
    }
  }
}

repositories {
  mavenCentral()
  google()
}

def kotlin_version = getExtOrDefault("kotlinVersion")

dependencies {
  implementation "com.facebook.react:react-android"
  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

  implementation project(":react-native-nitro-modules")

  // weee lib
  implementation 'com.sayweee.libs:wrapper-http:0.0.3-SNAPSHOT'
  implementation 'com.sayweee.libs:http-extension:0.0.4-SNAPSHOT'
  implementation 'com.sayweee.libs:widget-toaster:0.0.2-SNAPSHOT'
  implementation 'com.sayweee.libs:wrapper-core:0.0.2-SNAPSHOT'


  // weee module
  implementation 'com.sayweee.module:core-order:0.1.10-SNAPSHOT'

}

react {
  jsRootDir = file("../src/")
  libraryName = "WeeeModule"
  codegenJavaPackageName = "com.sayweee.react"
}
