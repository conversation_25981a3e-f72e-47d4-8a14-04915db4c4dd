package com.margelo.nitro.weee.service

import android.view.View
import com.facebook.react.uimanager.ThemedReactContext
import com.margelo.nitro.weee.data.AtcProductData

/**
 * Interface for ATC UI component operations
 * This abstracts all UI operations to avoid direct dependencies on main app UI components
 */
interface IAtcUIComponent {

    /**
     * Get the root view of the ATC component
     */
    val rootView: View

    /**
     * Initialize the UI component with context
     */
    fun initialize(context: ThemedReactContext?)

    /**
     * Set the current quantity display
     */
    fun setQuantity(quantity: Int, animate: Boolean = false)

    /**
     * Get the current displayed quantity
     */
    fun getCurrentQuantity(): Int

    /**
     * Set the operation style based on quantity and limits
     */
    fun setOperationStyle(
        currentQuantity: Int,
        minQuantity: Int,
        maxQuantity: Int
    )

    /**
     * Show volume price tip
     */
    fun showVolumePriceTip(priceDifference: Double, threshold: Int)

    /**
     * Show reached maximum quantity tip
     */
    fun showReachedMaxTip()

    /**
     * Show minimum purchase tip
     */
    fun showMinPurchaseTip(minQuantity: Int)

    /**
     * Show first add introduction for special products
     */
    fun showFirstAddIntroduction(product: AtcProductData)

    /**
     * Expand the component with animation
     */
    fun expandWithAnimation()

    /**
     * Expand the component without animation
     */
    fun expand()

    /**
     * Collapse the component with animation
     */
    fun collapseWithAnimation()

    /**
     * Collapse the component without animation
     */
    fun collapse()

    /**
     * Set click listener for operations
     */
    fun setOperationListener(listener: AtcOperationListener)

    /**
     * Dismiss any currently showing tips
     */
    fun dismissTips()

    /**
     * Update component styling and dimensions
     */
    fun updateStyling(factor: Int = 1)

    /**
     * Interface for handling user operations on the ATC component
     */
    interface AtcOperationListener {
        /**
         * Called when user clicks the decrease button
         */
        fun onDecreaseClicked()

        /**
         * Called when user clicks the increase button
         */
        fun onIncreaseClicked()

        /**
         * Called when user clicks on the quantity number
         */
        fun onQuantityClicked()

        /**
         * Get the product ID for this component
         */
        fun getProductId(): Int
    }
}
