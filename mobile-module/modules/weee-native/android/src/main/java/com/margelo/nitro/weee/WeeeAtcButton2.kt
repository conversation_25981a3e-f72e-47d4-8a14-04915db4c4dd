package com.margelo.nitro.weee

import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import com.facebook.react.uimanager.ThemedReactContext
import com.margelo.nitro.weee.data.AtcProductData
import com.margelo.nitro.weee.service.AtcServiceProvider
import com.margelo.nitro.weee.service.IAtcBusinessLogic
import com.margelo.nitro.weee.service.IAtcUIComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 * Nitro ATC Button component implementation
 * Uses dependency injection to avoid direct coupling with main app classes
 */
class WeeeAtcButton2(private var reactContext: ThemedReactContext?) : HybridWeeeAtcButton2Spec() {

    private val uiScope = CoroutineScope(Dispatchers.Main)
    private var productData: AtcProductData? = null
    private val businessLogic: IAtcBusinessLogic by lazy { AtcServiceProvider.getBusinessLogic() }
    private val uiComponent: IAtcUIComponent by lazy { AtcServiceProvider.createUIComponent(reactContext) }
    private val handler = Handler(Looper.getMainLooper())

    companion object {
        private const val TAG = "WeeeAtcButton2"
        private const val SOURCE_PREFIX = "nitro_atc_component"
    }

    init {
        try {
            // Check if dependencies are properly injected
            if (!AtcServiceProvider.isInitialized()) {
                throw IllegalStateException("AtcServiceProvider not initialized. Please register dependencies in main app.")
            }

            // Initialize UI component
            uiComponent.initialize(reactContext)
            setupUIEventHandlers()

            Log.d(TAG, "WeeeAtcButton2 initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize WeeeAtcButton2", e)
            throw e
        }
    }

    override var qty: Long
        get() = _qty
        set(value) {
            Log.d(TAG, "Setting qty to: $value")
            _qty = value
            updateUIFromProperties()
        }

    private var _qty: Long = 0

    override var text: String?
        get() = _text
        set(value) {
            Log.d(TAG, "Setting text to: $value")
            _text = value
            updateUIFromProperties()
        }

    private var _text: String? = null

    override var productJson: String?
        get() = _productJson
        set(value) {
            Log.d(TAG, "Setting productJson to: $value")
            _productJson = value
            parseAndSetProductData(value)
        }

    private var eventCallback: ((ButtonEvent) -> Unit)? = null

    override var onListener: (ButtonEvent) -> Unit
        get() = eventCallback ?: { listener(it) }
        set(value) {
            eventCallback = value
        }

    private fun listener(event: ButtonEvent): Unit {
        Log.d(TAG, "Received event: $event")
    }

    private fun sendQuantityChangeEvent(quantity: Double, productId: Double) {
        val event = ButtonEvent(
            id = "quantity_change_${System.currentTimeMillis()}",
            productId = productId,
            type = "quantity_change",
            action = if (quantity > _qty) "increase" else "decrease",
            quantity = quantity,
            status = null,
            animationType = if (quantity == 1.0) "add_to_cart" else if (quantity == 0.0) "remove_from_cart" else "quantity_update"
        )

        Log.d(TAG, "Sending quantity change event: $event")
        eventCallback?.invoke(event)
    }

    override fun onQuantityChange(quantity: Double, productId: Double) {
        sendQuantityChangeEvent(quantity, productId)
    }

    override fun onStatusChange(status: String, productId: Double) {
        sendStatusChangeEvent(status, productId)
    }

    override fun triggerAnimation(animationType: String, productId: Double) {
        sendAnimationTriggerEvent(animationType, productId)
    }

    private fun sendStatusChangeEvent(status: String, productId: Double) {
        val event = ButtonEvent(
            id = "status_change_${System.currentTimeMillis()}",
            productId = productId,
            type = "status_change",
            action = null,
            quantity = null,
            status = status,
            animationType = null
        )

        Log.d(TAG, "Sending status change event: $event")
        eventCallback?.invoke(event)
    }

    private fun sendAnimationTriggerEvent(animationType: String, productId: Double) {
        val event = ButtonEvent(
            id = "animation_trigger_${System.currentTimeMillis()}",
            productId = productId,
            type = "animation_trigger",
            action = null,
            quantity = null,
            status = null,
            animationType = animationType
        )

        Log.d(TAG, "Sending animation trigger event: $event")
        eventCallback?.invoke(event)
    }

    private var _productJson: String? = null

    override val view: View
        get() = uiComponent.rootView

    /**
     * Parse product JSON and update component state
     */
    private fun parseAndSetProductData(productJsonString: String?) {
        if (productJsonString != null) {
            try {
                productData = AtcProductData.fromJson(productJsonString)
                productData?.let { product ->
                    Log.d(TAG, "Product set: ${product.name}, id: ${product.id}")
                    initializeWithProduct(product)
                } ?: Log.e(TAG, "Failed to parse product JSON")
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing product JSON", e)
            }
        }
    }

    /**
     * Initialize component with product data
     */
    private fun initializeWithProduct(product: AtcProductData) {
        try {
            // Get current quantity from business logic
            val currentQuantity = businessLogic.getCurrentQuantity(product.id, product.productKey)

            // Initialize UI component with product data - this will trigger OpHelper setup in CartOpLayoutUIComponent
            uiComponent.initializeWithProductData(product)

            // Update UI state
            uiComponent.setOperationStyle(currentQuantity, product.minOrderQuantity, product.maxOrderQuantity)
            uiComponent.setQuantity(currentQuantity, false)

            // Update internal state
            _qty = currentQuantity.toLong()

            Log.d(TAG, "Initialized with product: ${product.name}, current quantity: $currentQuantity")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing with product", e)
        }
    }



    /**
     * Setup UI event handlers
     */
    private fun setupUIEventHandlers() {
        val listener = object : IAtcUIComponent.AtcOperationListener {
            override fun onDecreaseClicked() {
                executeDecreaseOperation()
            }

            override fun onIncreaseClicked() {
                executeIncreaseOperation()
            }

            override fun onQuantityClicked() {
                executeQuantityClickOperation()
            }

            override fun getProductId(): Int {
                return productData?.id ?: 0
            }
        }

        uiComponent.setOperationListener(listener)
    }

    /**
     * Execute decrease quantity operation
     * Note: The actual business logic is handled by CartOpLayoutUIComponent.executeOpHelperLeftLogic()
     * This method only handles the React Native event callbacks
     */
    private fun executeDecreaseOperation() {
        val product = productData ?: return

        try {
            // Get the updated quantity from UI component (after OpHelper logic execution)
            val newQuantity = uiComponent.getCurrentQuantity()

            // Update internal state to match UI
            _qty = newQuantity.toLong()

            // Trigger event callbacks to React Native
            onQuantityChange(newQuantity.toDouble(), product.id.toDouble())
            onStatusChange("decrease", product.id.toDouble())

            // Trigger animation events to React Native
            if (newQuantity == 0) {
                triggerAnimation("remove_from_cart", product.id.toDouble())
                triggerAnimation("collapse_editor", product.id.toDouble())
            } else {
                triggerAnimation("quantity_update", product.id.toDouble())
            }

            Log.d(TAG, "Decrease operation completed, new quantity: $newQuantity")

        } catch (e: Exception) {
            Log.e(TAG, "Error in decrease operation", e)
        }
    }

    /**
     * Execute increase quantity operation
     * Note: The actual business logic is handled by CartOpLayoutUIComponent.executeOpHelperRightLogic()
     * This method only handles the React Native event callbacks
     */
    private fun executeIncreaseOperation() {
        val product = productData ?: return

        try {
            // Get the current quantity before the operation (for animation logic)
            val currentQuantity = businessLogic.getCurrentQuantity(product.id, product.productKey)

            // Get the updated quantity from UI component (after OpHelper logic execution)
            val newQuantity = uiComponent.getCurrentQuantity()

            // Update internal state to match UI
            _qty = newQuantity.toLong()

            // Trigger event callbacks to React Native
            onQuantityChange(newQuantity.toDouble(), product.id.toDouble())
            onStatusChange("increase", product.id.toDouble())

            // Trigger animation events to React Native
            if (currentQuantity == 0 && newQuantity > 0) {
                triggerAnimation("add_to_cart", product.id.toDouble())
            } else {
                triggerAnimation("quantity_update", product.id.toDouble())
            }

            Log.d(TAG, "Increase operation completed: ${currentQuantity} -> ${newQuantity}")

        } catch (e: Exception) {
            Log.e(TAG, "Error in increase operation", e)
        }
    }

    /**
     * Execute quantity click operation
     * Note: The actual business logic is handled by CartOpLayoutUIComponent.executeOpHelperNumClickLogic()
     * This method only handles the React Native event callbacks
     */
    private fun executeQuantityClickOperation() {
        val product = productData ?: return

        try {
            // Get the updated quantity from UI component (after OpHelper logic execution)
            val currentQuantity = uiComponent.getCurrentQuantity()

            // Trigger event callbacks to React Native
            onStatusChange("numClick", product.id.toDouble())

            // Trigger animation events to React Native
            if (currentQuantity > 0) {
                triggerAnimation("expand_editor", product.id.toDouble())
                Log.d(TAG, "Quantity click - expanding editor for quantity: $currentQuantity")
            } else {
                // If no quantity, the OpHelper logic will have executed an increase operation
                triggerAnimation("add_to_cart", product.id.toDouble())
                Log.d(TAG, "Quantity click - executed increase operation")
            }

            Log.d(TAG, "Quantity click operation completed: quantity = $currentQuantity")

        } catch (e: Exception) {
            Log.e(TAG, "Error in quantity click operation", e)
        }
    }


    private fun updateUIFromProperties() {
        uiScope.launch {
        }
    }



}
