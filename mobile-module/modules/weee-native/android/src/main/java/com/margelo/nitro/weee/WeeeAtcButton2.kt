package com.margelo.nitro.weee

import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import com.facebook.react.uimanager.ThemedReactContext
import com.margelo.nitro.weee.data.AtcProductData
import com.margelo.nitro.weee.service.AtcServiceProvider
import com.margelo.nitro.weee.service.IAtcBusinessLogic
import com.margelo.nitro.weee.service.IAtcUIComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


/**
 * Nitro ATC Button component implementation
 * Uses dependency injection to avoid direct coupling with main app classes
 */
class WeeeAtcButton2(private var reactContext: ThemedReactContext?) : HybridWeeeAtcButton2Spec() {

    private val uiScope = CoroutineScope(Dispatchers.Main)
    private var productData: AtcProductData? = null
    private val businessLogic: IAtcBusinessLogic by lazy { AtcServiceProvider.getBusinessLogic() }
    private val uiComponent: IAtcUIComponent by lazy { AtcServiceProvider.createUIComponent(reactContext) }
    private val handler = Handler(Looper.getMainLooper())

    companion object {
        private const val TAG = "WeeeAtcButton2"
        private const val SOURCE_PREFIX = "nitro_atc_component"
    }

    init {
        try {
            // Check if dependencies are properly injected
            if (!AtcServiceProvider.isInitialized()) {
                throw IllegalStateException("AtcServiceProvider not initialized. Please register dependencies in main app.")
            }

            // Initialize UI component
            uiComponent.initialize(reactContext)
            setupUIEventHandlers()

            Log.d(TAG, "WeeeAtcButton2 initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize WeeeAtcButton2", e)
            throw e
        }
    }

    override var qty: Long
        get() = _qty
        set(value) {
            Log.d(TAG, "Setting qty to: $value")
            _qty = value
            updateUIFromProperties()
        }

    private var _qty: Long = 0

    override var text: String?
        get() = _text
        set(value) {
            Log.d(TAG, "Setting text to: $value")
            _text = value
            updateUIFromProperties()
        }

    private var _text: String? = null

    override var productJson: String?
        get() = _productJson
        set(value) {
            Log.d(TAG, "Setting productJson to: $value")
            _productJson = value
            parseAndSetProductData(value)
        }

    private var eventCallback: ((ButtonEvent) -> Unit)? = null

    override var onListener: (ButtonEvent) -> Unit
        get() = eventCallback ?: { listener(it) }
        set(value) {
            eventCallback = value
        }

    private fun listener(event: ButtonEvent): Unit {
        Log.d(TAG, "Received event: $event")
    }

    private fun sendQuantityChangeEvent(quantity: Double, productId: Double) {
        val event = ButtonEvent(
            id = "quantity_change_${System.currentTimeMillis()}",
            productId = productId,
            type = "quantity_change",
            action = if (quantity > _qty) "increase" else "decrease",
            quantity = quantity,
            status = null,
            animationType = if (quantity == 1.0) "add_to_cart" else if (quantity == 0.0) "remove_from_cart" else "quantity_update"
        )

        Log.d(TAG, "Sending quantity change event: $event")
        eventCallback?.invoke(event)
    }

    override fun onQuantityChange(quantity: Double, productId: Double) {
        sendQuantityChangeEvent(quantity, productId)
    }

    override fun onStatusChange(status: String, productId: Double) {
        sendStatusChangeEvent(status, productId)
    }

    override fun triggerAnimation(animationType: String, productId: Double) {
        sendAnimationTriggerEvent(animationType, productId)
    }

    private fun sendStatusChangeEvent(status: String, productId: Double) {
        val event = ButtonEvent(
            id = "status_change_${System.currentTimeMillis()}",
            productId = productId,
            type = "status_change",
            action = null,
            quantity = null,
            status = status,
            animationType = null
        )

        Log.d(TAG, "Sending status change event: $event")
        eventCallback?.invoke(event)
    }

    private fun sendAnimationTriggerEvent(animationType: String, productId: Double) {
        val event = ButtonEvent(
            id = "animation_trigger_${System.currentTimeMillis()}",
            productId = productId,
            type = "animation_trigger",
            action = null,
            quantity = null,
            status = null,
            animationType = animationType
        )

        Log.d(TAG, "Sending animation trigger event: $event")
        eventCallback?.invoke(event)
    }

    private var _productJson: String? = null

    override val view: View
        get() = uiComponent.rootView

    /**
     * Parse product JSON and update component state
     */
    private fun parseAndSetProductData(productJsonString: String?) {
        if (productJsonString != null) {
            try {
                productData = AtcProductData.fromJson(productJsonString)
                productData?.let { product ->
                    Log.d(TAG, "Product set: ${product.name}, id: ${product.id}")
                    initializeWithProduct(product)
                } ?: Log.e(TAG, "Failed to parse product JSON")
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing product JSON", e)
            }
        }
    }

    /**
     * Initialize component with product data
     */
    private fun initializeWithProduct(product: AtcProductData) {
        try {
            // Get current quantity from business logic
            val currentQuantity = businessLogic.getCurrentQuantity(product.id, product.productKey)

            // Update UI state
            uiComponent.setOperationStyle(currentQuantity, product.minOrderQuantity, product.maxOrderQuantity)
            uiComponent.setQuantity(currentQuantity, false)

            // Update internal state
            _qty = currentQuantity.toLong()

            Log.d(TAG, "Initialized with product: ${product.name}, current quantity: $currentQuantity")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing with product", e)
        }
    }

    /**
     * Setup UI event handlers
     */
    private fun setupUIEventHandlers() {
        val listener = object : IAtcUIComponent.AtcOperationListener {
            override fun onDecreaseClicked() {
                executeDecreaseOperation()
            }

            override fun onIncreaseClicked() {
                executeIncreaseOperation()
            }

            override fun onQuantityClicked() {
                executeQuantityClickOperation()
            }

            override fun getProductId(): Int {
                return productData?.id ?: 0
            }
        }

        uiComponent.setOperationListener(listener)
    }

    /**
     * Execute decrease quantity operation
     */
    private fun executeDecreaseOperation() {
        val product = productData ?: return

        try {
            val currentQuantity = businessLogic.getCurrentQuantity(product.id, product.productKey)
            val newQuantity = businessLogic.calculateNewQuantity(
                isAdd = false,
                currentQuantity = currentQuantity,
                minQuantity = product.minOrderQuantity,
                maxQuantity = product.maxOrderQuantity,
                volumeThreshold = product.volumeThreshold
            )

            // Check if should show volume price tip
            if (businessLogic.shouldShowVolumePriceTip(currentQuantity, newQuantity, product.volumeThreshold)) {
                val priceDiff = businessLogic.getVolumePriceDifference(product.volumePrice, product.price)
                uiComponent.showVolumePriceTip(priceDiff, product.volumeThreshold)
            }

            // Update UI
            uiComponent.setOperationStyle(newQuantity, product.minOrderQuantity, product.maxOrderQuantity)
            uiComponent.setQuantity(newQuantity, true)

            // Update cart through business logic
            val source = businessLogic.buildNewSource(product.id, SOURCE_PREFIX)
            val success = businessLogic.updateProductInCart(
                product.id, product.productKey, newQuantity, source, product.productType, product.referValue
            )

            if (success) {
                // Update internal state
                _qty = newQuantity.toLong()

                // Trigger event callbacks
                onQuantityChange(newQuantity.toDouble(), product.id.toDouble())
                onStatusChange("decrease", product.id.toDouble())

            // 触发减购动画
            if (newQuantity == 0) {
                triggerAnimation("remove_from_cart", product.id.toDouble())
                // 数量为0时收起编辑器
                uiComponent.collapseWithAnimation()
                triggerAnimation("collapse_editor", product.id.toDouble())
            } else {
                triggerAnimation("quantity_update", product.id.toDouble())
            }

                // Log analytics
                businessLogic.logCartAction("decrease", product.id, currentQuantity, newQuantity, source)

                Log.d(TAG, "Decrease operation: ${currentQuantity} -> ${newQuantity}")
            } else {
                Log.e(TAG, "Failed to update cart for decrease operation")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error in decrease operation", e)
        }
    }

    /**
     * Execute increase quantity operation
     */
    private fun executeIncreaseOperation() {
        val product = productData ?: return

        try {
            val currentQuantity = businessLogic.getCurrentQuantity(product.id, product.productKey)
            val newQuantity = businessLogic.calculateNewQuantity(
                isAdd = true,
                currentQuantity = currentQuantity,
                minQuantity = product.minOrderQuantity,
                maxQuantity = product.maxOrderQuantity,
                volumeThreshold = product.volumeThreshold
            )

            // Notify other components about status change
            businessLogic.notifyProductStatusChange(product.id)

            // Update UI
            uiComponent.setOperationStyle(newQuantity, product.minOrderQuantity, product.maxOrderQuantity)

            val reachedMax = businessLogic.isMaxQuantityReached(currentQuantity, newQuantity)
            if (reachedMax) {
                uiComponent.setQuantity(newQuantity, true)
                uiComponent.showReachedMaxTip()
            } else {
                uiComponent.setQuantity(newQuantity, true)

                // Show appropriate tips
                if (businessLogic.shouldShowMinPurchaseTip(currentQuantity, newQuantity, product.volumeThreshold)) {
                    uiComponent.showMinPurchaseTip(newQuantity)
                }

                // Show first add introduction if needed
                if (businessLogic.shouldShowFirstAddIntroduction(product, currentQuantity, newQuantity)) {
                    uiComponent.showFirstAddIntroduction(product)
                }

                // Update cart through business logic
                val source = businessLogic.buildNewSource(product.id, SOURCE_PREFIX)
                val success = businessLogic.updateProductInCart(
                    product.id, product.productKey, newQuantity, source, product.productType, product.referValue
                )

                if (success) {
                    // Log analytics
                    businessLogic.logCartAction("increase", product.id, currentQuantity, newQuantity, source)
                } else {
                    Log.e(TAG, "Failed to update cart for increase operation")
                }
            }

            // Update internal state
            _qty = newQuantity.toLong()

            // Trigger event callbacks
            onQuantityChange(newQuantity.toDouble(), product.id.toDouble())
            onStatusChange("increase", product.id.toDouble())

            // 触发加购动画
            if (currentQuantity == 0 && newQuantity > 0) {
                triggerAnimation("add_to_cart", product.id.toDouble())
            } else {
                triggerAnimation("quantity_update", product.id.toDouble())
            }

            Log.d(TAG, "Increase operation: ${currentQuantity} -> ${newQuantity}")

        } catch (e: Exception) {
            Log.e(TAG, "Error in increase operation", e)
        }
    }

    /**
     * Execute quantity click operation
     */
    private fun executeQuantityClickOperation() {
        val product = productData ?: return

        try {
            val currentQuantity = businessLogic.getCurrentQuantity(product.id, product.productKey)

            if (currentQuantity > 0) {
                // If has quantity, expand the editor interface
                businessLogic.notifyProductStatusChange(product.id)
                uiComponent.setOperationStyle(currentQuantity, product.minOrderQuantity, product.maxOrderQuantity)
                uiComponent.expandWithAnimation()

                // 触发展开编辑器动画
                triggerAnimation("expand_editor", product.id.toDouble())
            } else {
                // If no quantity, execute add operation
                executeIncreaseOperation()
            }

            Log.d(TAG, "Quantity click operation: quantity = $currentQuantity")

        } catch (e: Exception) {
            Log.e(TAG, "Error in quantity click operation", e)
        }
    }


    private fun updateUIFromProperties() {
        uiScope.launch {
        }
    }



}
