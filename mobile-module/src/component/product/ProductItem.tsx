import React, {useState, useCallback} from 'react';
import {View, Text, StyleSheet, Image, Alert} from 'react-native';
import {ProductBean} from '@/api/model';
import {WeeeAtcButton2} from 'weee-native';
import 'globalcss';

type ProductItemProps = {
  item: ProductBean;
};

const ProductItem = ({item}: ProductItemProps) => {
  const [quantity, setQuantity] = useState(0);

  // 构建产品 JSON 数据，使用 ProductBean 的实际字段
  const productData = {
    id: item.id,
    name: item.name,
    price: item.price,
    volume_price: item.volume_price_support ? item.volume_price : undefined,
    volume_threshold: item.volume_threshold || 3,
    min_order_quantity: item.min_order_quantity || 1,
    max_order_quantity:
      item.max_order_quantity || item.product_max_order_quantity || 99,
    product_key: item.product_key,
    category: item.category_name || item.category || 'General',
    is_pantry: item.is_pantry || false,
    is_limit_product: item.is_limit_product || false,
    remaining_count: item.remaining_count || 50,
    volume_price_support: item.volume_price_support || false,
    productType: item.is_pantry ? 'pantry' : 'normal',
    referValue: item.view_link || '',
  };

  const productJson = JSON.stringify(productData);

  // 错误处理：如果关键数据缺失，显示错误信息
  if (!item.id || !item.name) {
    console.warn('ProductItem: Missing required product data', item);
  }

  // 处理数量变化
  const handleQuantityChange = useCallback(
    (event: {quantity: number; productId: number}) => {
      console.log(`Product ${item.name} quantity changed to:`, event.quantity);
      setQuantity(event.quantity);

      // TODO: 这里可以调用购物车 API 更新数量
      // CartManager.updateQuantity(event.productId, event.quantity);

      // 可以添加错误处理
      try {
        // 调用购物车更新 API
        // await CartService.updateQuantity(event.productId, event.quantity);
      } catch (error) {
        console.error('Failed to update cart:', error);
        // 回滚数量变化
        setQuantity(prevQuantity => prevQuantity);
      }
    },
    [item.name],
  );

  // 处理状态变化
  const handleStatusChange = useCallback(
    (event: {status: string; productId: number}) => {
      console.log(`Product ${item.name} status changed:`, event.status);

      // 可以根据状态执行不同的操作
      switch (event.status) {
        case 'increase':
          // 增加数量时的逻辑
          console.log(`Increasing quantity for product ${event.productId}`);
          break;
        case 'decrease':
          // 减少数量时的逻辑
          console.log(`Decreasing quantity for product ${event.productId}`);
          break;
        case 'numClick':
          // 点击数量时的逻辑，可以显示数量选择器
          Alert.alert(
            '选择数量',
            `商品: ${item.name}\n当前数量: ${quantity}\n价格: $${item.price}${
              item.volume_price_support
                ? `\n批量价格: $${item.volume_price} (满${item.volume_threshold}件)`
                : ''
            }`,
            [
              {text: '取消', style: 'cancel'},
              {text: '确定', onPress: () => console.log('Confirmed quantity')},
            ],
          );
          break;
        default:
          console.log(`Unknown status: ${event.status}`);
      }
    },
    [
      item.name,
      item.price,
      item.volume_price,
      item.volume_price_support,
      item.volume_threshold,
      quantity,
    ],
  );

  return (
    <View className="flex-row items-center justify-between bg-white p-4 ">
      <Image source={{uri: item.img}} style={styles.productImage} />

      <View style={styles.productInfo}>
        {/* 品牌名 */}
        {/* <Text className="text-surface-100-fg-minor enki-body-2xs-medium line-clamp-1">
          {(item as any).brand || '品牌'}
        </Text> */}

        {/* 产品名称 */}
        <Text style={styles.productName} numberOfLines={2}>
          {item.name}
        </Text>

        <View style={styles.productSubItem}>
          <View style={styles.productSubInfo}>
            {/* 价格行 */}
            <View style={styles.priceRow}>
              <Text style={styles.price}>{item.price}</Text>
              {item.base_price && (
                <Text style={styles.originalPrice}>{item.base_price}</Text>
              )}
            </View>

            {/* 促销标签 */}
          </View>

          <WeeeAtcButton2
            className="h-[40px] w-[100px]"
            qty={BigInt(quantity)}
            productJson={productJson}
            onQuantityChange={handleQuantityChange}
            onStatusChange={handleStatusChange}
            // style={{
            //   ...styles.atcButton,
            // }}
          />
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
  },

  productSubItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productImage: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginRight: 16,
    backgroundColor: '#F8F8F8',
  },
  productInfo: {
    flex: 1,
    paddingRight: 12,
  },
  productSubInfo: {
    width: 100,
  },
  brandName: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
    fontWeight: '500',
  },
  productName: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 22,
    marginBottom: 8,
    fontWeight: '400',
  },
  priceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  price: {
    fontSize: 18,
    color: '#E53E3E',
    marginRight: 8,
    fontWeight: '400',
  },
  originalPrice: {
    fontSize: 14,
    color: '#999999',
    textDecorationLine: 'line-through',
  },
  promotionRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 6,
  },
  discountBadge: {
    backgroundColor: '#E53E3E',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  discountText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  cashbackBadge: {
    backgroundColor: '#F7D917',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  cashbackText: {
    color: '#333333',
    fontSize: 10,
    fontWeight: 'bold',
  },
  limitedBadge: {
    backgroundColor: '#38B2AC',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  limitedText: {
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
  },
  buttonContainer: {
    width: 120,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  atcButton: {
    // 额外的原生样式支持
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  buttonSize: {
    // 明确设置尺寸，确保原生组件正确显示
    width: '100%',
    height: 36, // h-9 对应 36px
    minHeight: 36,
  },
});

export default ProductItem;
