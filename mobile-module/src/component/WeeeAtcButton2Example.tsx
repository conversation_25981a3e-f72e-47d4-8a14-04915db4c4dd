import React, { useState, useCallback, useRef } from 'react';
import { View, Text, StyleSheet, Animated, Easing, Alert } from 'react-native';
import { WeeeAtcButton2 } from 'weee-native';

interface Product {
  id: number;
  name: string;
  price: string;
  quantity: number;
}

export default function WeeeAtcButton2Example() {
  const [products, setProducts] = useState<Product[]>([
    { id: 1, name: '苹果 - 富士', price: '$3.99', quantity: 0 },
    { id: 2, name: '香蕉 - 有机', price: '$2.49', quantity: 0 },
    { id: 3, name: '橙子 - 新鲜', price: '$4.99', quantity: 1 },
  ]);

  // 动画相关
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;

  // 处理数量变化
  const handleQuantityChange = useCallback((event: {quantity: number; productId: number}) => {
    const { quantity, productId } = event;
    console.log(`🔄 Quantity changed for product ${productId}: ${quantity}`);
    
    setProducts(prev => prev.map(product => 
      product.id === productId 
        ? { ...product, quantity }
        : product
    ));
  }, []);

  // 处理状态变化
  const handleStatusChange = useCallback((event: {status: string; productId: number}) => {
    const { status, productId } = event;
    console.log(`📱 Status changed for product ${productId}: ${status}`);
  }, []);

  // 处理动画触发
  const handleAnimationTrigger = useCallback((event: {animationType: string; productId: number}) => {
    const { animationType, productId } = event;
    console.log(`🎬 Animation triggered for product ${productId}: ${animationType}`);
    
    // 根据动画类型执行不同的动画
    switch (animationType) {
      case 'add_to_cart':
        // 首次加购动画 - 放大缩小效果
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.2,
            duration: 150,
            easing: Easing.out(Easing.ease),
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 150,
            easing: Easing.in(Easing.ease),
            useNativeDriver: true,
          }),
        ]).start();
        
        // 显示提示
        Alert.alert('🛒 加购成功', `商品 ${productId} 已添加到购物车！`);
        break;
        
      case 'remove_from_cart':
        // 移除动画 - 淡出效果
        Animated.timing(opacityAnim, {
          toValue: 0.3,
          duration: 200,
          useNativeDriver: true,
        }).start(() => {
          // 动画完成后恢复透明度
          Animated.timing(opacityAnim, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }).start();
        });
        
        Alert.alert('🗑️ 移除成功', `商品 ${productId} 已从购物车移除！`);
        break;
        
      case 'quantity_update':
        // 数量更新动画 - 轻微震动
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.05,
            duration: 100,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 100,
            useNativeDriver: true,
          }),
        ]).start();
        break;
        
      case 'expand_editor':
        // 展开编辑器动画 - 弹性效果
        Animated.spring(scaleAnim, {
          toValue: 1.1,
          friction: 3,
          tension: 40,
          useNativeDriver: true,
        }).start(() => {
          Animated.spring(scaleAnim, {
            toValue: 1,
            friction: 3,
            tension: 40,
            useNativeDriver: true,
          }).start();
        });
        break;
    }
  }, [scaleAnim, opacityAnim]);

  // 处理购物车更新
  const handleCartUpdate = useCallback((productId: number, quantity: number, action: 'add' | 'remove' | 'update') => {
    console.log(`🛒 Cart update: ${action} product ${productId}, quantity: ${quantity}`);
    
    // 这里可以调用实际的购物车API
    // 例如：CartAPI.updateCart(productId, quantity, action);
  }, []);

  return (
    <View style={styles.container}>
      <Text style={styles.title}>WeeeAtcButton2 动画测试</Text>
      <Text style={styles.subtitle}>测试原生端加购时的动画效果</Text>
      
      {products.map((product) => (
        <Animated.View 
          key={product.id} 
          style={[
            styles.productItem,
            {
              transform: [{ scale: scaleAnim }],
              opacity: opacityAnim,
            }
          ]}
        >
          <View style={styles.productInfo}>
            <Text style={styles.productName}>{product.name}</Text>
            <Text style={styles.productPrice}>{product.price}</Text>
            <Text style={styles.currentQuantity}>当前数量: {product.quantity}</Text>
          </View>
          
          <WeeeAtcButton2
            style={styles.atcButton}
            qty={BigInt(product.quantity)}
            productJson={JSON.stringify({
              id: product.id,
              name: product.name,
              price: product.price,
              minOrderQuantity: 1,
              maxOrderQuantity: 99,
              volumeThreshold: 10,
              productKey: `product_${product.id}`,
              productType: 'normal',
              referValue: 0
            })}
            onQuantityChange={handleQuantityChange}
            onStatusChange={handleStatusChange}
            onAnimationTrigger={handleAnimationTrigger}
            onCartUpdate={handleCartUpdate}
          />
        </Animated.View>
      ))}
      
      <View style={styles.instructions}>
        <Text style={styles.instructionTitle}>测试说明:</Text>
        <Text style={styles.instructionText}>• 点击 "+" 按钮测试加购动画</Text>
        <Text style={styles.instructionText}>• 点击 "-" 按钮测试减购动画</Text>
        <Text style={styles.instructionText}>• 从0加到1会触发 "add_to_cart" 动画</Text>
        <Text style={styles.instructionText}>• 从1减到0会触发 "remove_from_cart" 动画</Text>
        <Text style={styles.instructionText}>• 其他数量变化会触发 "quantity_update" 动画</Text>
        <Text style={styles.instructionText}>• 点击数量数字会触发 "expand_editor" 动画</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  productItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  productInfo: {
    flex: 1,
    marginRight: 15,
  },
  productName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  productPrice: {
    fontSize: 16,
    color: '#e74c3c',
    fontWeight: '500',
    marginBottom: 4,
  },
  currentQuantity: {
    fontSize: 14,
    color: '#666',
  },
  atcButton: {
    width: 120,
    height: 40,
  },
  instructions: {
    marginTop: 30,
    padding: 20,
    backgroundColor: 'white',
    borderRadius: 10,
  },
  instructionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  instructionText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
});
