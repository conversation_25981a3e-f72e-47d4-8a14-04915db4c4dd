package com.sayweee.weee.react.atc;

import android.content.Context;
import android.text.style.TextAppearanceSpan;
import android.view.View;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.facebook.react.uimanager.ThemedReactContext;
import com.margelo.nitro.weee.data.AtcProductData;
import com.margelo.nitro.weee.service.IAtcUIComponent;
import com.sayweee.weee.R;
import com.sayweee.weee.module.cart.bean.ProductBean;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.widget.op.CartOpLayout;

/**
 * Adapter that wraps CartOpLayout to implement IAtcUIComponent interface
 * This bridges the main app's CartOpLayout with the nitro module's abstraction
 */
public class CartOpLayoutUIComponent implements IAtcUIComponent {

    private CartOpLayout cartOpLayout;
    private AtcOperationListener operationListener;

    @Override
    public View getRootView() {
        return cartOpLayout;
    }

    @Override
    public void initialize(ThemedReactContext context) {
        // Create CartOpLayout instance
        cartOpLayout = new CartOpLayout(context);
        cartOpLayout.setLayoutParams(new CartOpLayout.LayoutParams(200, 100));

        // Initialize with proper styling
        initOpLayoutStyling(context);

        // Set up internal event listeners
        setupInternalListeners();
    }

    @Override
    public void setQuantity(int quantity, boolean animate) {
        if (cartOpLayout != null) {
            cartOpLayout.setTextNum(quantity, animate);
        }
    }

    @Override
    public int getCurrentQuantity() {
        if (cartOpLayout != null) {
            return cartOpLayout.getTextNum();
        }
        return 0;
    }

    @Override
    public void setOperationStyle(int currentQuantity, int minQuantity, int maxQuantity) {
        if (cartOpLayout != null) {
            cartOpLayout.setOpStyle(currentQuantity, minQuantity, maxQuantity);
        }
    }

    @Override
    public void showVolumePriceTip(double priceDifference, int threshold) {
        if (cartOpLayout != null) {
            cartOpLayout.showVolumePriceTips(priceDifference, threshold);
        }
    }

    @Override
    public void showReachedMaxTip() {
        if (cartOpLayout != null) {
            cartOpLayout.showReachedTips();
        }
    }

    @Override
    public void showMinPurchaseTip(int minQuantity) {
        if (cartOpLayout != null) {
            cartOpLayout.showMinPurchaseTips(minQuantity);
        }
    }

    @Override
    public void showFirstAddIntroduction(AtcProductData product) {
        if (cartOpLayout != null && product != null) {
            Context context = cartOpLayout.getContext();

            // Convert AtcProductData to format expected by OpHelper
            boolean isAlcohol = "alcohol".equalsIgnoreCase(product.getCategory());
            boolean isPantry = product.isPantry();

            if (isAlcohol || isPantry) {
                // Use OpHelper's showFirstAddIntroduce method
                // Note: This creates a minimal ProductBean-like object for compatibility
                OpHelper.showFirstAddIntroduce(context, (View) cartOpLayout,
                        (ProductBean) createProductBeanProxy(product), 0, 1, 5);
            }
        }
    }

    @Override
    public void expandWithAnimation() {
        if (cartOpLayout != null) {
            cartOpLayout.expandWithAnim();
        }
    }

    @Override
    public void expand() {
        if (cartOpLayout != null) {
            cartOpLayout.expand();
        }
    }

    @Override
    public void collapseWithAnimation() {
        if (cartOpLayout != null) {
            cartOpLayout.collapseWithAnim();
        }
    }

    @Override
    public void collapse() {
        if (cartOpLayout != null) {
            cartOpLayout.collapse();
        }
    }

    @Override
    public void setOperationListener(AtcOperationListener listener) {
        this.operationListener = listener;
    }

    @Override
    public void dismissTips() {
        if (cartOpLayout != null) {
            cartOpLayout.dismissTips();
        }
    }

    @Override
    public void updateStyling(int factor) {
        if (cartOpLayout != null) {
            initOpLayoutStyling(cartOpLayout.getContext(), factor);
        }
    }

    /**
     * Initialize CartOpLayout with proper styling (adapted from ReactAtcButtonManager)
     */
    private void initOpLayoutStyling(Context context) {
        initOpLayoutStyling(context, 1);
    }

    private void initOpLayoutStyling(Context context, int factor) {
        if (cartOpLayout == null || context == null) return;

        cartOpLayout.setTransValue((int) (cartOpLayout.getTransValue() * factor));

        ConstraintLayout layoutOpView = cartOpLayout.findViewById(R.id.layout_op_view);
        if (layoutOpView != null) {
            ConstraintLayout.LayoutParams clp = (ConstraintLayout.LayoutParams) layoutOpView.getLayoutParams();
            clp.height = (int) (context.getResources().getDimensionPixelOffset(R.dimen.prop_size_atc_mini) * factor);
            layoutOpView.setLayoutParams(clp);

            TextView tvTips = cartOpLayout.findViewById(R.id.tv_tips);
            if (tvTips != null) {
                cartOpLayout.setClipChildren(false);

                cartOpLayout.setTipsTextSizeFactor(factor);

                TextAppearanceSpan normal = cartOpLayout.getTipsNormalTextAppearanceSpan();
                if (normal != null) {
                    cartOpLayout.setTipsNormalTextAppearanceSpan(new TextAppearanceSpan(
                        normal.getFamily(),
                        normal.getTextStyle(),
                        (int) (normal.getTextSize() * factor),
                        normal.getTextColor(),
                        normal.getLinkTextColor()
                    ));
                }

                TextAppearanceSpan bold = cartOpLayout.getTipsBoldTextAppearanceSpan();
                if (bold != null) {
                    cartOpLayout.setTipsBoldTextAppearanceSpan(new TextAppearanceSpan(
                        bold.getFamily(),
                        bold.getTextStyle(),
                        (int) (bold.getTextSize() * factor),
                        bold.getTextColor(),
                        bold.getLinkTextColor()
                    ));
                }
            }
        }
    }

    /**
     * Setup internal event listeners to bridge CartOpLayout events to our interface
     */
    private void setupInternalListeners() {
        if (cartOpLayout == null) return;

        cartOpLayout.setOnOperateListener(new CartOpLayout.OnCartOpListener() {
            @Override
            public void operateLeft(View view) {
                if (operationListener != null) {
                    operationListener.onDecreaseClicked();
                }
            }

            @Override
            public void operateRight(View view) {
                if (operationListener != null) {
                    operationListener.onIncreaseClicked();
                }
            }

            @Override
            public void onNumClick(View view) {
                if (operationListener != null) {
                    operationListener.onQuantityClicked();
                }
            }

            @Override
            public int getProductId() {
                if (operationListener != null) {
                    return operationListener.getProductId();
                }
                return 0;
            }
        });
    }

    /**
     * Create a minimal ProductBean-like object for compatibility with existing methods
     * This is a temporary bridge to avoid changing OpHelper immediately
     */
    private Object createProductBeanProxy(AtcProductData product) {
        // This would return a minimal object that implements the required methods
        // for OpHelper.showFirstAddIntroduce to work
        return new Object() {
            public boolean isAlcohol() {
                return "alcohol".equalsIgnoreCase(product.getCategory());
            }

            public boolean is_pantry() {
                return product.isPantry();
            }

            public String getCategory() {
                return product.getCategory();
            }

            public int getProductId() {
                return product.getId();
            }
        };
    }
}
