package com.sayweee.weee.react.atc;

import android.content.Context;
import android.text.style.TextAppearanceSpan;
import android.util.Log;
import android.view.View;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.facebook.react.uimanager.ThemedReactContext;
import com.margelo.nitro.weee.data.AtcProductData;
import com.margelo.nitro.weee.service.IAtcUIComponent;
import com.sayweee.weee.R;
import com.sayweee.weee.module.cart.bean.ProductBean;

import com.sayweee.weee.module.cart.service.OrderHelper;
import com.sayweee.weee.module.cate.product.adapter.OpHelper;
import com.sayweee.weee.global.manager.OrderManager;
import com.sayweee.weee.utils.CommonTools;
import com.sayweee.weee.utils.DecimalTools;
import com.sayweee.weee.widget.op.CartOpLayout;
import com.sayweee.core.order.SharedOrderViewModel;

import java.util.HashMap;
import java.util.Map;

/**
 * Adapter that wraps CartOpLayout to implement IAtcUIComponent interface
 * This bridges the main app's CartOpLayout with the nitro module's abstraction
 */
public class CartOpLayoutUIComponent implements IAtcUIComponent {

    private CartOpLayout cartOpLayout;
    private AtcOperationListener operationListener;

    @Override
    public View getRootView() {
        return cartOpLayout;
    }

    @Override
    public void initialize(ThemedReactContext context) {
        // Create CartOpLayout instance
        cartOpLayout = new CartOpLayout(context);
        cartOpLayout.setLayoutParams(new CartOpLayout.LayoutParams(200, 100));

        // Initialize with proper styling
        initOpLayoutStyling(context);

        // Note: setupInternalListeners() is called in initializeWithProductData()
        // after OpHelper.helperOp() to prevent the listener from being overwritten
    }

    @Override
    public void initializeWithProductData(AtcProductData product) {
        if (cartOpLayout != null && product != null) {
            try {
                // Convert AtcProductData to ProductBean
                ProductBean productBean = convertAtcProductDataToProductBean(product);

                if (productBean != null) {
                    // Call OpHelper.helperOp to set up the CartOpLayout properly
                    // This is the key missing piece that makes animations work
                    String source = "nitro_atc_component";
                    Map<String, Object> element = new HashMap<>();
                    Map<String, Object> ctx = new HashMap<>();

                    OpHelper.helperOp(cartOpLayout, productBean, productBean, source, element, ctx);

                    // IMPORTANT: Wrap the OpHelper listener instead of replacing it
                    // This preserves the animation and cart logic while adding our callbacks
                    setupWrappedListeners(productBean, source, element, ctx);

                    Log.d("CartOpLayoutUIComponent", "OpHelper.helperOp called successfully for product: " + product.getName());
                }
            } catch (Exception e) {
                Log.e("CartOpLayoutUIComponent", "Error in initializeWithProductData", e);
            }
        }
    }

    @Override
    public void setQuantity(int quantity, boolean animate) {
        if (cartOpLayout != null) {
            cartOpLayout.setTextNum(quantity, animate);
        }
    }

    @Override
    public int getCurrentQuantity() {
        if (cartOpLayout != null) {
            return cartOpLayout.getTextNum();
        }
        return 0;
    }

    @Override
    public void setOperationStyle(int currentQuantity, int minQuantity, int maxQuantity) {
        if (cartOpLayout != null) {
            cartOpLayout.setOpStyle(currentQuantity, minQuantity, maxQuantity);
        }
    }

    @Override
    public void showVolumePriceTip(double priceDifference, int threshold) {
        if (cartOpLayout != null) {
            cartOpLayout.showVolumePriceTips(priceDifference, threshold);
        }
    }

    @Override
    public void showReachedMaxTip() {
        if (cartOpLayout != null) {
            cartOpLayout.showReachedTips();
        }
    }

    @Override
    public void showMinPurchaseTip(int minQuantity) {
        if (cartOpLayout != null) {
            cartOpLayout.showMinPurchaseTips(minQuantity);
        }
    }

    @Override
    public void showFirstAddIntroduction(AtcProductData product) {
        if (cartOpLayout != null && product != null) {
            Context context = cartOpLayout.getContext();

            // Convert AtcProductData to format expected by OpHelper
            boolean isAlcohol = "alcohol".equalsIgnoreCase(product.getCategory());
            boolean isPantry = product.isPantry();

            if (isAlcohol || isPantry) {
                // Use OpHelper's showFirstAddIntroduce method
                // Note: This creates a minimal ProductBean-like object for compatibility
                OpHelper.showFirstAddIntroduce(context, (View) cartOpLayout,
                        (ProductBean) createProductBeanProxy(product), 0, 1, 5);
            }
        }
    }

    @Override
    public void expandWithAnimation() {
        if (cartOpLayout != null) {
            cartOpLayout.expandWithAnim();
        }
    }

    @Override
    public void expand() {
        if (cartOpLayout != null) {
            cartOpLayout.expand();
        }
    }

    @Override
    public void collapseWithAnimation() {
        if (cartOpLayout != null) {
            cartOpLayout.collapseWithAnim();
        }
    }

    @Override
    public void collapse() {
        if (cartOpLayout != null) {
            cartOpLayout.collapse();
        }
    }

    @Override
    public void setOperationListener(AtcOperationListener listener) {
        this.operationListener = listener;
    }

    @Override
    public void dismissTips() {
        if (cartOpLayout != null) {
            cartOpLayout.dismissTips();
        }
    }

    @Override
    public void updateStyling(int factor) {
        if (cartOpLayout != null) {
            initOpLayoutStyling(cartOpLayout.getContext(), factor);
        }
    }

    /**
     * Initialize CartOpLayout with proper styling (adapted from ReactAtcButtonManager)
     */
    private void initOpLayoutStyling(Context context) {
        initOpLayoutStyling(context, 1);
    }

    private void initOpLayoutStyling(Context context, int factor) {
        if (cartOpLayout == null || context == null) return;

        cartOpLayout.setTransValue((int) (cartOpLayout.getTransValue() * factor));

        ConstraintLayout layoutOpView = cartOpLayout.findViewById(R.id.layout_op_view);
        if (layoutOpView != null) {
            ConstraintLayout.LayoutParams clp = (ConstraintLayout.LayoutParams) layoutOpView.getLayoutParams();
            clp.height = (int) (context.getResources().getDimensionPixelOffset(R.dimen.prop_size_atc_mini) * factor);
            layoutOpView.setLayoutParams(clp);

            TextView tvTips = cartOpLayout.findViewById(R.id.tv_tips);
            if (tvTips != null) {
                cartOpLayout.setClipChildren(false);

                cartOpLayout.setTipsTextSizeFactor(factor);

                TextAppearanceSpan normal = cartOpLayout.getTipsNormalTextAppearanceSpan();
                if (normal != null) {
                    cartOpLayout.setTipsNormalTextAppearanceSpan(new TextAppearanceSpan(
                        normal.getFamily(),
                        normal.getTextStyle(),
                        (int) (normal.getTextSize() * factor),
                        normal.getTextColor(),
                        normal.getLinkTextColor()
                    ));
                }

                TextAppearanceSpan bold = cartOpLayout.getTipsBoldTextAppearanceSpan();
                if (bold != null) {
                    cartOpLayout.setTipsBoldTextAppearanceSpan(new TextAppearanceSpan(
                        bold.getFamily(),
                        bold.getTextStyle(),
                        (int) (bold.getTextSize() * factor),
                        bold.getTextColor(),
                        bold.getLinkTextColor()
                    ));
                }
            }
        }
    }

    /**
     * Setup wrapped event listeners that preserve OpHelper logic while adding our callbacks
     * Since we can't access the original listener, we recreate the OpHelper logic here
     */
    private void setupWrappedListeners(ProductBean productBean, String source, Map<String, Object> element, Map<String, Object> ctx) {
        if (cartOpLayout == null) {
            Log.w("CartOpLayoutUIComponent", "setupWrappedListeners: cartOpLayout is null");
            return;
        }

        Log.d("CartOpLayoutUIComponent", "Setting up wrapped listeners with OpHelper logic");

        cartOpLayout.setOnOperateListener(new CartOpLayout.OnCartOpListener() {
            @Override
            public void operateLeft(View view) {
                Log.d("CartOpLayoutUIComponent", "operateLeft called - executing OpHelper logic + custom callback");

                // Execute OpHelper's operateLeft logic
                executeOpHelperLeftLogic(view, productBean, source, element, ctx);

                // Then call our custom callback
                if (operationListener != null) {
                    operationListener.onDecreaseClicked();
                } else {
                    Log.w("CartOpLayoutUIComponent", "operationListener is null in operateLeft");
                }
            }

            @Override
            public void operateRight(View view) {
                Log.d("CartOpLayoutUIComponent", "operateRight called - executing OpHelper logic + custom callback");

                // Execute OpHelper's operateRight logic
                executeOpHelperRightLogic(view, productBean, source, element, ctx);

                // Then call our custom callback
                if (operationListener != null) {
                    operationListener.onIncreaseClicked();
                } else {
                    Log.w("CartOpLayoutUIComponent", "operationListener is null in operateRight");
                }
            }

            @Override
            public void onNumClick(View view) {
                Log.d("CartOpLayoutUIComponent", "onNumClick called - executing OpHelper logic + custom callback");

                // Execute OpHelper's onNumClick logic
                executeOpHelperNumClickLogic(view, productBean);

                // Then call our custom callback
                if (operationListener != null) {
                    operationListener.onQuantityClicked();
                } else {
                    Log.w("CartOpLayoutUIComponent", "operationListener is null in onNumClick");
                }
            }

            @Override
            public int getProductId() {
                if (operationListener != null) {
                    return operationListener.getProductId();
                }
                return productBean.getProductId();
            }
        });

        Log.d("CartOpLayoutUIComponent", "Wrapped listeners setup completed");
    }

    /**
     * Execute OpHelper's operateLeft logic (decrease quantity)
     */
    private void executeOpHelperLeftLogic(View view, ProductBean bean, String source, Map<String, Object> element, Map<String, Object> ctx) {
        try {
            int productId = bean.getProductId();
            String productKey = bean.getProductKey();
            int volumeThreshold = bean.getVolumeThreshold();

            // Get current quantity from OrderManager (simplified approach)
            int lastNum = OrderManager.get().getCartItemNum(productId);

            // Calculate new quantity
            int num = OrderHelper.editNum(false, lastNum, bean.min_order_quantity, bean.getOrderMaxQuantity(), volumeThreshold);

            // Show volume price tips if needed
            if (lastNum == volumeThreshold && lastNum - num == 1) {
                double diff = DecimalTools.subtract(bean.volume_price, bean.price);
                cartOpLayout.showVolumePriceTips(diff, volumeThreshold);
            }

            // Update UI with animation
            cartOpLayout.setOpStyle(num, bean.min_order_quantity, bean.getOrderMaxQuantity());
            cartOpLayout.setTextNum(num, true); // Enable animation

            // Update cart data
            OrderManager.get().setProductChanged(productId, num, source, bean.getProductType(), bean.getReferValue(), productKey, null, source);

            Log.d("CartOpLayoutUIComponent", "OpHelper left logic executed: " + lastNum + " -> " + num);
        } catch (Exception e) {
            Log.e("CartOpLayoutUIComponent", "Error in executeOpHelperLeftLogic", e);
        }
    }

    /**
     * Execute OpHelper's operateRight logic (increase quantity)
     */
    private void executeOpHelperRightLogic(View view, ProductBean bean, String source, Map<String, Object> element, Map<String, Object> ctx) {
        try {
            int productId = bean.getProductId();
            String productKey = bean.getProductKey();
            int volumeThreshold = bean.getVolumeThreshold();

            // Get current quantity from OrderManager (simplified approach)
            int lastNum = OrderManager.get().getCartItemNum(productId);

            // Calculate new quantity
            int num = OrderHelper.editNum(true, lastNum, bean.min_order_quantity, bean.getOrderMaxQuantity(), volumeThreshold);

            // Notify other components about the operation
            SharedOrderViewModel.get().productOpStatusData.postValue(productId);

            // Update UI with animation
            cartOpLayout.setOpStyle(num, bean.min_order_quantity, bean.getOrderMaxQuantity());

            boolean reachedMaxNum = lastNum > 0 && lastNum == num;
            if (reachedMaxNum) {
                cartOpLayout.setTextNum(num, true); // Enable animation
                cartOpLayout.showReachedTips();
            } else {
                if (lastNum <= 0 && num > 1) {
                    cartOpLayout.setTextNum(num, true); // Enable animation
                    if (volumeThreshold <= 0) {
                        cartOpLayout.showMinPurchaseTips(num);
                    }
                } else {
                    cartOpLayout.setTextNum(num, true); // Enable animation
                    // Show first add introduction if needed
                    OpHelper.showFirstAddIntroduce(view.getContext(), view, bean, lastNum, num, 5);
                }

                // Update cart data
                OrderManager.get().setProductChanged(productId, num, source, bean.getProductType(), bean.getReferValue(), productKey, null, source);
            }

            Log.d("CartOpLayoutUIComponent", "OpHelper right logic executed: " + lastNum + " -> " + num);
        } catch (Exception e) {
            Log.e("CartOpLayoutUIComponent", "Error in executeOpHelperRightLogic", e);
        }
    }

    /**
     * Execute OpHelper's onNumClick logic
     */
    private void executeOpHelperNumClickLogic(View view, ProductBean bean) {
        try {
            int productId = bean.getProductId();

            // Get current quantity from OrderManager (simplified approach)
            int num = OrderManager.get().getCartItemNum(productId);

            if (num > 0) {
                // Notify other components about the operation
                SharedOrderViewModel.get().productOpStatusData.postValue(productId);

                // Update style and expand with animation
                cartOpLayout.setOpStyle(num, bean.min_order_quantity, bean.getOrderMaxQuantity());
                cartOpLayout.expandWithAnim();
            } else {
                // If no quantity, treat as increase
                executeOpHelperRightLogic(view, bean, "nitro_atc_component", new HashMap<>(), new HashMap<>());
            }

            Log.d("CartOpLayoutUIComponent", "OpHelper numClick logic executed for quantity: " + num);
        } catch (Exception e) {
            Log.e("CartOpLayoutUIComponent", "Error in executeOpHelperNumClickLogic", e);
        }
    }

    /**
     * Create a minimal ProductBean-like object for compatibility with existing methods
     * This is a temporary bridge to avoid changing OpHelper immediately
     */
    private Object createProductBeanProxy(AtcProductData product) {
        // This would return a minimal object that implements the required methods
        // for OpHelper.showFirstAddIntroduce to work
        return new Object() {
            public boolean isAlcohol() {
                return "alcohol".equalsIgnoreCase(product.getCategory());
            }

            public boolean is_pantry() {
                return product.isPantry();
            }

            public String getCategory() {
                return product.getCategory();
            }

            public int getProductId() {
                return product.getId();
            }
        };
    }

    /**
     * Convert AtcProductData to ProductBean for OpHelper compatibility
     */
    private ProductBean convertAtcProductDataToProductBean(AtcProductData product) {
        try {
            ProductBean productBean = new ProductBean();
            productBean.id = product.getId();
            productBean.product_key = product.getProductKey();
            productBean.min_order_quantity = product.getMinOrderQuantity();
            productBean.max_order_quantity = product.getMaxOrderQuantity();
            productBean.volume_threshold = product.getVolumeThreshold();
            productBean.name = product.getName();
            productBean.price = product.getPrice();
            productBean.volume_price = product.getVolumePrice();
            productBean.product_type = product.getProductType();
//            productBean.refer_value = product.getReferValue();
            return productBean;
        } catch (Exception e) {
            Log.e("CartOpLayoutUIComponent", "Error converting AtcProductData to ProductBean", e);
            return null;
        }
    }
}
